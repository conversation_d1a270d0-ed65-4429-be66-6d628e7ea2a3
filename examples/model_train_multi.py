#!/usr/bin/env python
"""
Example training a model from config

This script demonstrates:
1. Builds a model from a config
2. Prepares data from config
3. Runs training with specified arguments
4. Registers the model in a registry for easy loading

Usage:
    python model_train.py --config configs/v1_cones_dense_gaussian_standard_mish.yaml --test

Optional arguments

"""
#%%
import torch
from torch.utils.data import DataLoader
import argparse
import sys
import time

import lightning as pl
from lightning.pytorch.loggers import WandbLogger
from lightning.pytorch.profilers import PyTorchProfiler

from pathlib import Path
import numpy as np
import os
from DataYatesV1.models import build_model, initialize_model_components, get_name_from_config
from DataYatesV1.models.config_loader import load_config, load_multidataset_config
from DataYatesV1.models.lightning import PLCoreVisionModel, MultiDatasetPLCore
from DataYatesV1.models.model_manager import ModelRegistry
from DataYatesV1.utils.data import prepare_data, prepare_multidataset_data
from DataYatesV1.utils.general import ensure_tensor
from DataYatesV1.utils.torch import get_free_device
import yaml
from DataYatesV1.models.utils.general import ValidateOnTrainStart
import sys
import codename

# Set random seed for reproducibility
torch.manual_seed(42)
np.random.seed(42)

registry_dir = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/model_registry_multi")
registry = ModelRegistry(registry_dir)

#%%
def create_dataloaders(train_dset, val_dset, batch_size=256):
    """Create DataLoader objects for training."""
    train_loader = DataLoader(
        train_dset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=os.cpu_count()-4,
        pin_memory=True
    )

    val_loader = DataLoader(
        val_dset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=os.cpu_count()-4,
        pin_memory=True
    )

    return train_loader, val_loader


def create_multidataset_loaders(train_datasets_dict, val_datasets_dict, batch_size=256):
    """Create DataLoader objects for multidataset training."""
    from lightning.pytorch.utilities import CombinedLoader

    # Create individual dataloaders for each dataset
    train_loaders = {}
    val_loaders = {}

    for dataset_name, train_dset in train_datasets_dict.items():
        train_loader = DataLoader(
            train_dset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=max(1, os.cpu_count()-4),
            pin_memory=True
        )
        train_loaders[dataset_name] = train_loader

    for dataset_name, val_dset in val_datasets_dict.items():
        val_loader = DataLoader(
            val_dset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=max(1, os.cpu_count()-4),
            pin_memory=True
        )
        val_loaders[dataset_name] = val_loader

    # Combine loaders using Lightning's CombinedLoader
    # Use "max_size_cycle" mode to cycle through smaller datasets
    combined_train_loader = CombinedLoader(train_loaders, mode="max_size_cycle")
    combined_val_loader = CombinedLoader(val_loaders, mode="max_size_cycle")

    return combined_train_loader, combined_val_loader


class CompileSubmodules(pl.Callback):
    """
    Compile convnet and recurrent once the model is on GPU.
    Attach to Trainer callbacks list.
    """
    def __init__(self, convnet=True, recurrent=True, compile_kwargs=None):
        self.convnet = convnet
        self.recurrent = recurrent
        self.kw = compile_kwargs or dict(mode="reduce-overhead")
        self._done = False

    def on_fit_start(self, trainer, pl_module):
        if self._done:       # only once, even if you resume training
            return

        m = pl_module.model   # your assembled core model

        if self.convnet:
            m.convnet = torch.compile(m.convnet, **self.kw)

        if self.recurrent and hasattr(m, "recurrent"):
            m.recurrent = torch.compile(m.recurrent, **self.kw)

        self._done = True
        rank = trainer.global_rank if hasattr(trainer, "global_rank") else 0
        if rank == 0:
            print("[torch.compile] convnet / recurrent compiled")


class FP16SafetyCallback(pl.Callback):
    """
    Callback to ensure critical components remain in FP32 during FP16 training.
    Forces BatchNorm, custom normalization layers, and readouts to FP32.
    """
    def __init__(self):
        self._done = False

    def on_fit_start(self, trainer, pl_module):
        if self._done or trainer.precision != "16-mixed":
            return

        model = pl_module.model
        fp32_count = 0

        # Force critical components to FP32
        for name, module in model.named_modules():
            # BatchNorm layers
            if isinstance(module, (torch.nn.BatchNorm1d, torch.nn.BatchNorm2d, torch.nn.BatchNorm3d)):
                module.float()
                fp32_count += 1

            # Custom normalization layers
            elif 'RMSNorm' in module.__class__.__name__ or 'LayerNorm' in module.__class__.__name__:
                module.float()
                fp32_count += 1

            elif 'GlobalResponseNorm' in module.__class__.__name__:
                module.float()
                fp32_count += 1

            # Readout layers
            elif 'readout' in name.lower() or 'Readout' in module.__class__.__name__:
                module.float()
                fp32_count += 1

        self._done = True
        rank = trainer.global_rank if hasattr(trainer, "global_rank") else 0
        if rank == 0:
            print(f"[FP16 Safety] Forced {fp32_count} critical components to FP32")


class GradientMonitorCallback(pl.Callback):
    """
    Callback to monitor gradients for NaN/Inf values and gradient norms during FP16 training.
    """
    def __init__(self, log_frequency=100):
        self.log_frequency = log_frequency
        self.step_count = 0

    def on_after_backward(self, trainer, pl_module):
        if trainer.precision != "16-mixed":
            return

        self.step_count += 1

        if self.step_count % self.log_frequency == 0:
            # Check for NaN/Inf gradients
            nan_count = 0
            inf_count = 0
            total_norm = 0.0
            param_count = 0

            for param in pl_module.parameters():
                if param.grad is not None:
                    grad = param.grad
                    nan_count += torch.isnan(grad).sum().item()
                    inf_count += torch.isinf(grad).sum().item()
                    total_norm += grad.norm().item() ** 2
                    param_count += 1

            total_norm = total_norm ** 0.5

            # Log gradient statistics
            pl_module.log('grad_norm', total_norm, prog_bar=False)

            if nan_count > 0 or inf_count > 0:
                print(f"[Gradient Monitor] Step {self.step_count}: NaN={nan_count}, Inf={inf_count}, Norm={total_norm:.4f}")

            # Check gradient scaler state if available
            if hasattr(trainer.strategy, 'scaler'):
                scaler = trainer.strategy.scaler
                pl_module.log('grad_scale', scaler.get_scale(), prog_bar=False)


def train_model(config, train_loader, val_loader, dataset_config, checkpoint_dir, dataset_config_path, model_name=None, device=None, max_epochs=2, compile=False, test_mode=False, early_stopping_patience=5):
    """Train a model and save checkpoints.

    Args:
        config: Model configuration dictionary
        train_loader: Training data loader
        val_loader: Validation data loader
        dataset_config: Dataset configuration
        checkpoint_dir: Directory to save checkpoints
        dataset_config_path: Path to the dataset configuration file
        model_name: Model name to use (if None, will be generated from config)
        device: Device specification (string or torch.device). Can be 'auto', 'cpu', 'cuda', 'cuda:0', etc.
        max_epochs: Maximum number of epochs to train
        compile: Whether to compile the model with torch.compile
        test_mode: If True, limit batches and enable profiler for testing
        early_stopping_patience: Number of epochs with no improvement after which training will be stopped
    """
    # Use provided model name or generate from config
    if model_name is None:
        model_name = get_name_from_config(config)

    # Handle device specification
    if device is None or device == 'auto':
        device = get_free_device()
        print(f"Auto-selected device: {device}")
    elif isinstance(device, str):
        if device.lower() == 'cpu':
            device = torch.device('cpu')
        elif device.lower().startswith('cuda'):
            device = torch.device(device)
        else:
            raise ValueError(f"Invalid device specification: {device}")
        print(f"Using specified device: {device}")
    elif isinstance(device, torch.device):
        print(f"Using device: {device}")
    else:
        raise ValueError(f"Invalid device type: {type(device)}")

    # Determine accelerator and devices for PyTorch Lightning
    if device.type == 'cpu':
        accelerator = 'cpu'
        devices = 'auto'  # Let Lightning handle CPU device selection
    elif device.type == 'cuda':
        accelerator = 'gpu'
        devices = [device.index] if device.index is not None else 'auto'
    else:
        # Handle other device types (mps, etc.)
        accelerator = 'auto'
        devices = 'auto'

    # Create the trainer with checkpoint and early stopping callbacks
    checkpoint_callback = pl.pytorch.callbacks.ModelCheckpoint(
        dirpath=str(checkpoint_dir),
        filename=model_name+'-{epoch:02d}-{val_loss:.4f}',
        monitor='val_loss',
        mode='min',
        save_top_k=2,
        save_last=True
    )

    # Add early stopping callback to prevent overfitting
    early_stopping_callback = pl.pytorch.callbacks.EarlyStopping(
        monitor='val_loss',
        patience=early_stopping_patience,
        verbose=True,
        mode='min'
    )

    callbacks = [checkpoint_callback, early_stopping_callback, ValidateOnTrainStart()]

    # check if in interactive notebook. if not. compile model for speedup
    if (not 'ipykernel' in sys.modules) and compile:
        print("Adding compile callback to trainer...")
        compile_cb = CompileSubmodules(
        convnet=True,
        recurrent=True,
        compile_kwargs=dict(fullgraph=False, dynamic=True, mode="default")
        )
        callbacks.append(compile_cb)

    # Configure profiler if in test mode
    profiler = None
    if test_mode:
        print("Test mode enabled: Using PyTorch profiler")
        profiler = PyTorchProfiler(
            dirpath=str(checkpoint_dir),
            filename="profiler-traces",
            schedule=torch.profiler.schedule(
                wait=1,
                warmup=1,
                active=3,
            ),
            record_shapes=True,
            profile_memory=True,
            with_stack=True
        )
    else:
        profiler = "simple"  # Use simple profiler in non-test mode

    print("\nCalculating baseline firing rates...")
    fr = 0
    n = 0
    for dset in train_loader.dataset.dsets:
        fr += dset.covariates['robs'].sum(0)
        n += dset.covariates['robs'].shape[0]
    baseline_rates = fr / n
    inv_softplus = lambda x, beta=1: torch.log(torch.exp(beta*x) - 1) / beta

    # Create Lightning module
    pl_model = PLCoreVisionModel(
        model_class=build_model,  # Pass the factory function
        model_config=config,      # Pass the configuration
        optimizer='AdamW',
        optim_kwargs={'lr': 5e-4, 'weight_decay': 1e-5},
        accumulate_grad_batches=1,
        dataset_info=dataset_config  # Pass dataset information
    )

    # Configure trainer parameters
    trainer_kwargs = {
        "callbacks": callbacks,
        "accelerator": accelerator,
        "devices": devices,
        "logger": WandbLogger(
            project='Digital Twin',
            name=model_name,
            save_code=True,
            entity='yateslab',
            save_dir=str(checkpoint_dir),
        ),
        "default_root_dir": str(checkpoint_dir),
        "max_epochs": max_epochs if not test_mode else 1,  # Limit epochs in test mode
        "check_val_every_n_epoch": 1,
        "profiler": profiler,
    }

    # Add batch limits if in test mode
    if test_mode:
        trainer_kwargs.update({
            "limit_train_batches": 5,
            "limit_val_batches": 3,
        })

    # initialize model
    initialize_model_components(pl_model.model, init_bias=inv_softplus(baseline_rates))
    # pl_model.model.readout.bias.data = ensure_tensor(inv_softplus(baseline_rates), device=pl_model.model.readout.bias.device, dtype=pl_model.model.readout.bias.dtype)

    # Create trainer and train
    trainer = pl.Trainer(**trainer_kwargs)

    # Start timing
    start_time = time.time()

    # Train the model
    trainer.fit(pl_model, train_loader, val_loader)

    # End timing
    end_time = time.time()
    training_time = end_time - start_time

    # Get the best model path
    best_model_path = checkpoint_callback.best_model_path

    # Print training summary and profiler information
    print("\n===== TRAINING SUMMARY =====")
    print(f"Total training time: {training_time:.2f} seconds")

    # Print metrics
    print("\nTraining metrics:")
    for metric_name, metric_value in trainer.callback_metrics.items():
        if isinstance(metric_value, torch.Tensor):
            print(f"- {metric_name}: {metric_value.item():.6f}")
        else:
            print(f"- {metric_name}: {metric_value}")

    # Print additional profiler information if in test mode
    if test_mode and isinstance(profiler, PyTorchProfiler):
        print("\n===== PROFILER INFORMATION =====")

        # Print profiler output location
        profiler_output = os.path.join(checkpoint_dir, "profiler-traces")
        print(f"Detailed profiler traces saved to: {profiler_output}")
        print("You can view these traces with TensorBoard or Chrome trace viewer")

        # Print model performance summary
        print("\nModel compilation status:")
        if compile:
            print("- Model was compiled with torch.compile")
            print("- Convnet and recurrent modules were compiled")
        else:
            print("- Model was NOT compiled (running in eager mode)")

    # Print model architecture summary
    print("\nModel architecture summary:")
    print(f"- Frontend: {config['frontend']['type']}")
    print(f"- Convnet: {config['convnet']['type']}")
    print(f"- Recurrent: {config.get('recurrent', {}).get('type', 'None')}")
    print(f"- Readout: {config['readout']['type']}")

    # Print training configuration
    print("\nTraining configuration:")
    print(f"- Early stopping: Enabled (patience={early_stopping_patience})")
    print(f"- Max epochs: {max_epochs}")
    print(f"- Model compilation: {compile}")
    print("============================")

    # Register the model
    model_id = f"{model_name}_epoch_{trainer.current_epoch}"

    registry.register_model(
        model_id=model_id,
        checkpoint_path=best_model_path,
        config_path=config_path,
        metrics={'val_loss': trainer.callback_metrics.get('val_loss', 0).item()},
        metadata={
            'epochs': trainer.current_epoch,
            'training_time': training_time,
            'batch_size': train_loader.batch_size,
            'compiled': compile
        },
        dataset_config_path=dataset_config_path
    )

    return best_model_path


def train_multidataset_model(train_config_path, model_name=None, device=None, max_epochs=25,
                           compile=False, test_mode=False, early_stopping_patience=5, batch_size=256,
                           use_fp16=False, fp16_opt_level="O1"):
    """Train a multidataset model and save checkpoints.

    Args:
        train_config_path: Path to the multidataset training configuration file
        model_name: Model name to use (if None, will be generated from config)
        device: Device specification (string or torch.device)
        max_epochs: Maximum number of epochs to train
        compile: Whether to compile the model with torch.compile
        test_mode: If True, limit batches and enable profiler for testing
        early_stopping_patience: Number of epochs with no improvement after which training will be stopped
        batch_size: Batch size for training and validation
        use_fp16: Whether to use FP16 mixed precision training
        fp16_opt_level: FP16 optimization level ("O1" for conservative, "O2" for aggressive)
    """
    print(f"Loading multidataset training config from: {train_config_path}")

    # Load configurations
    model_config, dataset_configs = load_multidataset_config(train_config_path)

    print(f"Loaded model config (type: {model_config.get('model_type')})")
    print(f"Loaded {len(dataset_configs)} dataset configs")

    # Prepare multidataset data
    train_datasets_dict, val_datasets_dict, updated_dataset_configs = prepare_multidataset_data(
        dataset_configs, use_fp16=use_fp16
    )

    # Create combined dataloaders
    train_loader, val_loader = create_multidataset_loaders(
        train_datasets_dict, val_datasets_dict, batch_size=batch_size
    )

    # Generate model name if not provided
    if model_name is None:
        base_name = get_name_from_config(model_config)
        random_suffix = codename.codename()
        model_name = f"{base_name}_multidataset_{random_suffix}"

    print(f"Model name: {model_name}")

    # Handle device specification
    if device is None or device == 'auto':
        device = get_free_device()
        print(f"Auto-selected device: {device}")
    elif isinstance(device, str):
        if device.lower() == 'cpu':
            device = torch.device('cpu')
        elif device.lower().startswith('cuda'):
            device = torch.device(device)
        else:
            raise ValueError(f"Invalid device specification: {device}")
        print(f"Using specified device: {device}")

    # Determine accelerator and devices for PyTorch Lightning
    if device.type == 'cpu':
        accelerator = 'cpu'
        devices = 'auto'
    elif device.type == 'cuda':
        accelerator = 'gpu'
        devices = [device.index] if device.index is not None else 'auto'
    else:
        accelerator = 'auto'
        devices = 'auto'

    # Create checkpoint directory
    checkpoint_dir = Path(f"/mnt/ssd/YatesMarmoV1/conv_model_fits/runs/{model_name}")
    checkpoint_dir.mkdir(parents=True, exist_ok=True)

    # Create callbacks
    checkpoint_callback = pl.pytorch.callbacks.ModelCheckpoint(
        dirpath=str(checkpoint_dir),
        filename=model_name+'-{epoch:02d}-{val_loss_total:.4f}',
        monitor='val_loss_total',
        mode='min',
        save_top_k=2,
        save_last=True
    )

    early_stopping_callback = pl.pytorch.callbacks.EarlyStopping(
        monitor='val_loss_total',
        patience=early_stopping_patience,
        verbose=True,
        mode='min'
    )

    callbacks = [checkpoint_callback, early_stopping_callback, ValidateOnTrainStart()]

    # Add FP16 safety callback if using FP16
    if use_fp16:
        print("Adding FP16 safety callback to trainer...")
        fp16_safety_cb = FP16SafetyCallback()
        callbacks.append(fp16_safety_cb)

        print("Adding gradient monitoring callback to trainer...")
        grad_monitor_cb = GradientMonitorCallback(log_frequency=100)
        callbacks.append(grad_monitor_cb)

    # Add compile callback if requested
    if (not 'ipykernel' in sys.modules) and compile:
        print("Adding compile callback to trainer...")
        compile_cb = CompileSubmodules(
            convnet=True,
            recurrent=True,
            compile_kwargs=dict(fullgraph=False, dynamic=True, mode="default")
        )
        callbacks.append(compile_cb)

    # Configure profiler
    profiler = None
    if test_mode:
        print("Test mode enabled: Using PyTorch profiler")
        profiler = PyTorchProfiler(
            dirpath=str(checkpoint_dir),
            filename="profiler-traces",
            schedule=torch.profiler.schedule(wait=1, warmup=1, active=3),
            record_shapes=True,
            profile_memory=True,
            with_stack=True
        )
    else:
        profiler = "simple"

    # Calculate baseline firing rates for each dataset
    print("\nCalculating baseline firing rates for each dataset...")
    baseline_rates_list = []

    for dataset_idx, (dataset_name, dataset_config) in enumerate(zip(train_datasets_dict.keys(), updated_dataset_configs)):
        train_dset = train_datasets_dict[dataset_name]

        fr = 0
        n = 0
        for dset in train_dset.dsets:
            fr += dset.covariates['robs'].sum(0)
            n += dset.covariates['robs'].shape[0]
        baseline_rates = fr / n
        baseline_rates_list.append(baseline_rates)

        print(f"Dataset {dataset_idx}: {len(dataset_config['cids'])} units, "
              f"mean rate: {baseline_rates.mean().item():.4f}")

    inv_softplus = lambda x, beta=1: torch.log(torch.exp(beta*x) - 1) / beta
    baseline_rates_list = [inv_softplus(rates) for rates in baseline_rates_list]

    # Adjust learning rate for FP16 training
    base_lr = 5e-4
    if use_fp16:
        # Reduce learning rate for FP16 stability
        base_lr = 2.5e-4
        print(f"Reduced learning rate to {base_lr} for FP16 training")

    # Create Lightning module
    pl_model = MultiDatasetPLCore(
        model_class=build_model,
        model_config=model_config,
        dataset_configs=updated_dataset_configs,
        optimizer='AdamW',
        optim_kwargs={'lr': base_lr, 'weight_decay': 1e-5},
        accumulate_grad_batches=1,
        dataset_info=updated_dataset_configs
    )

    # Configure trainer
    trainer_kwargs = {
        "callbacks": callbacks,
        "accelerator": accelerator,
        "devices": devices,
        "logger": WandbLogger(
            project='Digital Twin Multidataset',
            name=model_name,
            save_code=True,
            entity='yateslab',
            save_dir=str(checkpoint_dir),
        ),
        "default_root_dir": str(checkpoint_dir),
        "max_epochs": max_epochs if not test_mode else 1,
        "check_val_every_n_epoch": 1,
        "profiler": profiler,
    }

    # Add FP16 configuration if requested
    if use_fp16:
        trainer_kwargs["precision"] = "16-mixed"
        trainer_kwargs["gradient_clip_val"] = 1.0
        print(f"FP16 training enabled with precision='16-mixed' and gradient clipping=1.0")

    # Add batch limits if in test mode
    if test_mode:
        trainer_kwargs.update({
            "limit_train_batches": 5,
            "limit_val_batches": 3,
        })

    # Initialize model components
    initialize_model_components(pl_model.model, init_bias=baseline_rates_list)

    # Create trainer and train
    trainer = pl.Trainer(**trainer_kwargs)

    # Start timing
    start_time = time.time()

    # Train the model
    trainer.fit(pl_model, train_loader, val_loader)

    # End timing
    end_time = time.time()
    training_time = end_time - start_time

    # Get the best model path
    best_model_path = checkpoint_callback.best_model_path

    # Print training summary
    print("\n===== MULTIDATASET TRAINING SUMMARY =====")
    print(f"Total training time: {training_time:.2f} seconds")
    print(f"Number of datasets: {len(dataset_configs)}")

    for i, dataset_config in enumerate(updated_dataset_configs):
        print(f"Dataset {i}: {len(dataset_config['cids'])} units")

    # Print metrics
    print("\nTraining metrics:")
    for metric_name, metric_value in trainer.callback_metrics.items():
        if isinstance(metric_value, torch.Tensor):
            print(f"- {metric_name}: {metric_value.item():.6f}")
        else:
            print(f"- {metric_name}: {metric_value}")

    print("==========================================")

    # Register the model
    model_id = f"{model_name}_epoch_{trainer.current_epoch}"

    # Create metadata for multidataset model
    metadata = {
        'epochs': trainer.current_epoch,
        'training_time': training_time,
        'batch_size': batch_size,
        'compiled': compile,
        'num_datasets': len(dataset_configs),
        'total_units': sum(len(config['cids']) for config in updated_dataset_configs)
    }

    registry.register_model(
        model_id=model_id,
        checkpoint_path=best_model_path,
        config_path=str(train_config_path),  # Store the training config path
        metrics={'val_loss_total': trainer.callback_metrics.get('val_loss_total', 0).item()},
        metadata=metadata,
        dataset_config_path=[config.get('_config_path', '') for config in updated_dataset_configs]
    )

    return best_model_path

#%% Main execution
def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='Train and test neural network models (single or multidataset)')

    # Training mode selection
    parser.add_argument('--train-config', type=str,
                        help='Path to multidataset training configuration file (for multidataset training)')

    # Single dataset training (legacy)
    parser.add_argument('--config', type=str,
                        default='/mnt/ssd/YatesMarmoV1/conv_model_fits/configs/v1_cones_dense_gaussian_standard_mish.yaml',
                        help='Path to model configuration file (for single dataset training)')

    parser.add_argument('--dataset-config', type=str,
                        default='/mnt/ssd/YatesMarmoV1/conv_model_fits/configs/dataset_config.yaml',
                        help='Path to dataset configuration file (for single dataset training)')

    parser.add_argument('--compile', action='store_true',
                        help='Enable model compilation with torch.compile')

    parser.add_argument('--test', action='store_true',
                        help='Run in test mode with limited batches and profiling')

    parser.add_argument('--max-epochs', type=int, default=25,
                        help='Maximum number of training epochs')

    parser.add_argument('--batch-size', type=int, default=256,
                        help='Batch size for training and validation')

    parser.add_argument('--early-stopping-patience', type=int, default=5,
                        help='Number of epochs with no improvement after which training will be stopped')

    parser.add_argument('--device', type=str, default='auto',
                        help='Device to use for training. Options: "auto", "cpu", "cuda", "cuda:0", etc. Default: "auto" (automatically select best available device)')

    parser.add_argument('--no-codename', action='store_true',
                        help='Disable appending a random codename to the model name (codename is appended by default for uniqueness)')

    parser.add_argument('--fp16', action='store_true',
                        help='Enable FP16 mixed precision training for memory efficiency and speed')

    parser.add_argument('--fp16-opt-level', type=str, default='O1', choices=['O1', 'O2'],
                        help='FP16 optimization level: O1 (conservative) or O2 (aggressive). Default: O1')

    return parser.parse_args()

if __name__ == "__main__":
    # Parse command-line arguments
    args = parse_arguments()

    print(f"Device: {args.device}")
    print(f"Compilation enabled: {args.compile}")
    print(f"Test mode: {args.test}")
    print(f"Early stopping patience: {args.early_stopping_patience}")
    print(f"Append codename: {not args.no_codename}")
    print(f"FP16 training: {args.fp16}")
    if args.fp16:
        print(f"FP16 optimization level: {args.fp16_opt_level}")

    # Determine training mode
    if args.train_config:
        # Multidataset training mode
        print("=== MULTIDATASET TRAINING MODE ===")
        print(f"Using training config: {args.train_config}")

        # Generate model name with codename if requested
        model_name = None
        if not args.no_codename:
            # We'll generate the name in the training function
            pass

        # Train multidataset model
        best_model_path = train_multidataset_model(
            train_config_path=args.train_config,
            model_name=model_name,
            device=args.device,
            max_epochs=args.max_epochs,
            compile=args.compile,
            test_mode=args.test,
            early_stopping_patience=args.early_stopping_patience,
            batch_size=args.batch_size,
            use_fp16=args.fp16,
            fp16_opt_level=args.fp16_opt_level
        )

        print(f"\nMultidataset training complete!")
        print(f"Best model saved to: {best_model_path}")

    else:
        # Single dataset training mode (legacy)
        print("=== SINGLE DATASET TRAINING MODE ===")

        # Set paths based on arguments
        config_path = Path(args.config)
        dataset_config_path = Path(args.dataset_config)

        print(f"Using config: {config_path}")
        print(f"Using dataset config: {dataset_config_path}")

        # Load dataset configuration
        with open(dataset_config_path, 'r') as f:
            dataset_config = yaml.safe_load(f)

        # Prepare data using the dataset configuration
        train_dset, val_dset, dataset_config = prepare_data(dataset_config)
        train_loader, val_loader = create_dataloaders(train_dset, val_dset, batch_size=args.batch_size)

        # Update config with number of units
        config = load_config(config_path)
        config['readout']['params']['n_units'] = len(dataset_config['cids'])

        # Generate model name (potentially with codename) for checkpoint directory
        base_model_name = get_name_from_config(config)
        if not args.no_codename:
            random_suffix = codename.codename()
            full_model_name = f"{base_model_name}_{random_suffix}"
            print(f"Generated model name with codename: {full_model_name}")
        else:
            full_model_name = base_model_name
            print(f"Using base model name: {full_model_name}")

        checkpoint_dir = Path(f"/mnt/ssd/YatesMarmoV1/conv_model_fits/runs/{full_model_name}")
        checkpoint_dir.mkdir(parents=True, exist_ok=True)

        # Train the model
        best_model_path = train_model(
            config=config,
            train_loader=train_loader,
            val_loader=val_loader,
            dataset_config=dataset_config,
            checkpoint_dir=checkpoint_dir,
            dataset_config_path=dataset_config_path,
            model_name=full_model_name,
            device=args.device,
            max_epochs=args.max_epochs,
            compile=args.compile,
            test_mode=args.test,
            early_stopping_patience=args.early_stopping_patience
        )

        # Find and load the best model from the registry
        try:
            best_model, model_entry = registry.get_best_model(
                metric='val_loss',
                mode='min',
                config_match={'model_type': 'v1'},
                dataset_match={'session': dataset_config['session'], 'types': ['gaborium', 'backimage']}
            )
            print(f"Best model from registry: {model_entry['model_id']}")
        except Exception as e:
            print(f"Could not retrieve best model from registry: {e}")

    print("\nDone!")