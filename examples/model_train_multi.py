#!/usr/bin/env python
"""
Example training a model from config

This script demonstrates:
1. Builds a model from a config
2. Prepares data from config
3. Runs training with specified arguments
4. Registers the model in a registry for easy loading

Usage:
    python model_train.py --config configs/v1_cones_dense_gaussian_standard_mish.yaml --test

Optional arguments

"""
#%%
import torch
from torch.utils.data import DataLoader
import argparse
import sys
import time

import lightning as pl
from lightning.pytorch.loggers import WandbLogger
from lightning.pytorch.profilers import PyTorchProfiler

from pathlib import Path
import numpy as np
import os
from DataYatesV1.models import build_model, initialize_model_components, get_name_from_config
from DataYatesV1.models.config_loader import load_multidataset_config
from DataYatesV1.models.lightning import MultiDatasetPLCore
from DataYatesV1.models.model_manager import ModelRegistry
from DataYatesV1.utils.data import prepare_multidataset_data
from DataYatesV1.utils.general import ensure_tensor
from DataYatesV1.utils.torch import get_free_device
from DataYatesV1.models.utils.general import ValidateOnTrainStart
import sys
import codename

# Set random seed for reproducibility
torch.manual_seed(42)
np.random.seed(42)

registry_dir = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/model_registry_multi")
registry = ModelRegistry(registry_dir)

#%%
def debug_dataloader(dataloader, name="DataLoader", max_batches=3, timeout_seconds=30):
    """
    Debug a DataLoader to check for infinite loops or hanging issues.

    Args:
        dataloader: The DataLoader to test
        name: Name for logging
        max_batches: Maximum number of batches to test
        timeout_seconds: Timeout for each batch

    Returns:
        bool: True if DataLoader works, False if it hangs
    """
    import signal
    import time

    def timeout_handler(signum, frame):
        raise TimeoutError(f"DataLoader batch loading timed out after {timeout_seconds} seconds")

    print(f"\n🔍 Testing {name} (max {max_batches} batches, {timeout_seconds}s timeout per batch)...")

    try:
        for i, batch in enumerate(dataloader):
            if i >= max_batches:
                break

            # Set timeout for this batch
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(timeout_seconds)

            try:
                start_time = time.time()

                # Test basic batch properties
                if isinstance(batch, dict):
                    print(f"  Batch {i}: {len(batch)} keys")
                    for key, value in batch.items():
                        if hasattr(value, 'shape'):
                            print(f"    {key}: {value.shape} ({value.dtype})")
                        else:
                            print(f"    {key}: {type(value)}")
                else:
                    print(f"  Batch {i}: {type(batch)}")

                load_time = time.time() - start_time
                print(f"  ✅ Batch {i} loaded successfully in {load_time:.3f}s")

            except TimeoutError as e:
                print(f"  ❌ {e}")
                return False
            finally:
                signal.alarm(0)  # Cancel the alarm

        print(f"✅ {name} test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ {name} test failed: {e}")
        return False


def create_multidataset_loaders(train_datasets_dict, val_datasets_dict, batch_size=256, num_workers=None, debug_mode=False):
    """Create DataLoader objects for multidataset training."""
    from lightning.pytorch.utilities import CombinedLoader

    # Determine number of workers
    if num_workers is None:
        num_workers = 0  # Default to 0 to avoid hanging issues

    # In debug mode, force single-threaded loading
    if debug_mode:
        num_workers = 0
        print(f"Debug mode: Using {num_workers} DataLoader workers (single-threaded)")

    # Create individual dataloaders for each dataset
    train_loaders = {}
    val_loaders = {}

    for dataset_name, train_dset in train_datasets_dict.items():
        print(f"Creating DataLoaders for {dataset_name}...")

        train_loader = DataLoader(
            train_dset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=True,
            timeout=30 if num_workers > 0 else 0
        )
        train_loaders[dataset_name] = train_loader

    for dataset_name, val_dset in val_datasets_dict.items():
        val_loader = DataLoader(
            val_dset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=True,
            timeout=30 if num_workers > 0 else 0
        )
        val_loaders[dataset_name] = val_loader

    # Test individual DataLoaders if in debug mode
    if debug_mode:
        print("\n🔍 Testing individual DataLoaders...")
        for dataset_name in train_datasets_dict.keys():
            if not debug_dataloader(train_loaders[dataset_name], f"Train {dataset_name}", max_batches=2):
                print(f"❌ Train DataLoader for {dataset_name} failed!")
                return None, None
            if not debug_dataloader(val_loaders[dataset_name], f"Val {dataset_name}", max_batches=2):
                print(f"❌ Val DataLoader for {dataset_name} failed!")
                return None, None

    # Combine loaders using Lightning's CombinedLoader
    print("Creating combined DataLoaders...")
    combined_train_loader = CombinedLoader(train_loaders, mode="min_size")
    combined_val_loader = CombinedLoader(val_loaders, mode="min_size")

    # Test combined DataLoaders if in debug mode
    if debug_mode:
        print("\n🔍 Testing combined DataLoaders...")
        if not debug_dataloader(combined_train_loader, "Combined Train", max_batches=2):
            print("❌ Combined train DataLoader failed!")
            return None, None
        if not debug_dataloader(combined_val_loader, "Combined Val", max_batches=2):
            print("❌ Combined val DataLoader failed!")
            return None, None

    return combined_train_loader, combined_val_loader


class CompileSubmodules(pl.Callback):
    """
    Compile convnet and recurrent once the model is on GPU.
    Attach to Trainer callbacks list.
    """
    def __init__(self, convnet=True, recurrent=True, compile_kwargs=None):
        self.convnet = convnet
        self.recurrent = recurrent
        self.kw = compile_kwargs or dict(mode="reduce-overhead")
        self._done = False

    def on_fit_start(self, trainer, pl_module):
        if self._done:       # only once, even if you resume training
            return

        m = pl_module.model   # your assembled core model

        if self.convnet:
            m.convnet = torch.compile(m.convnet, **self.kw)

        if self.recurrent and hasattr(m, "recurrent"):
            m.recurrent = torch.compile(m.recurrent, **self.kw)

        self._done = True
        rank = trainer.global_rank if hasattr(trainer, "global_rank") else 0
        if rank == 0:
            print("[torch.compile] convnet / recurrent compiled")


class FP16SafetyCallback(pl.Callback):
    """
    Callback to ensure critical components remain in FP32 during FP16 training.
    Forces BatchNorm, custom normalization layers, and readouts to FP32.
    """
    def __init__(self):
        self._done = False

    def on_fit_start(self, trainer, pl_module):
        if self._done or trainer.precision != "16-mixed":
            return

        model = pl_module.model
        fp32_count = 0

        # Force critical components to FP32
        for name, module in model.named_modules():
            # BatchNorm layers
            if isinstance(module, (torch.nn.BatchNorm1d, torch.nn.BatchNorm2d, torch.nn.BatchNorm3d)):
                module.float()
                fp32_count += 1

            # Custom normalization layers
            elif 'RMSNorm' in module.__class__.__name__ or 'LayerNorm' in module.__class__.__name__:
                module.float()
                fp32_count += 1

            elif 'GlobalResponseNorm' in module.__class__.__name__:
                module.float()
                fp32_count += 1

            # Readout layers
            elif 'readout' in name.lower() or 'Readout' in module.__class__.__name__:
                module.float()
                fp32_count += 1

        self._done = True
        rank = trainer.global_rank if hasattr(trainer, "global_rank") else 0
        if rank == 0:
            print(f"[FP16 Safety] Forced {fp32_count} critical components to FP32")


class GradientMonitorCallback(pl.Callback):
    """
    Callback to monitor gradients for NaN/Inf values and gradient norms during FP16 training.
    """
    def __init__(self, log_frequency=100):
        self.log_frequency = log_frequency
        self.step_count = 0

    def on_after_backward(self, trainer, pl_module):
        if trainer.precision != "16-mixed":
            return

        self.step_count += 1

        if self.step_count % self.log_frequency == 0:
            # Check for NaN/Inf gradients
            nan_count = 0
            inf_count = 0
            total_norm = 0.0
            param_count = 0

            for param in pl_module.parameters():
                if param.grad is not None:
                    grad = param.grad
                    nan_count += torch.isnan(grad).sum().item()
                    inf_count += torch.isinf(grad).sum().item()
                    total_norm += grad.norm().item() ** 2
                    param_count += 1

            total_norm = total_norm ** 0.5

            # Log gradient statistics
            pl_module.log('grad_norm', total_norm, prog_bar=False)

            if nan_count > 0 or inf_count > 0:
                print(f"[Gradient Monitor] Step {self.step_count}: NaN={nan_count}, Inf={inf_count}, Norm={total_norm:.4f}")

            # Check gradient scaler state if available
            if hasattr(trainer.strategy, 'scaler'):
                scaler = trainer.strategy.scaler
                pl_module.log('grad_scale', scaler.get_scale(), prog_bar=False)


class NaNDebugCallback(pl.Callback):
    """
    Callback to debug NaN losses in FP16 training.
    """
    def __init__(self, check_every_n_steps=10):
        self.check_every_n_steps = check_every_n_steps
        self.step_count = 0

    def on_train_batch_end(self, trainer, pl_module, outputs, batch, batch_idx):
        self.step_count += 1

        if self.step_count % self.check_every_n_steps == 0:
            # Check if loss is NaN
            if torch.isnan(outputs).any():
                print(f"🚨 NaN loss detected at step {self.step_count}!")
                self._debug_batch(batch, pl_module)

    def _debug_batch(self, batch_dict, pl_module):
        """Debug a batch that produced NaN loss."""
        print("🔍 Debugging NaN loss...")

        for dataset_name, batch in batch_dict.items():
            print(f"\n--- Dataset: {dataset_name} ---")

            # Check input data
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    nan_count = torch.isnan(value).sum().item()
                    inf_count = torch.isinf(value).sum().item()
                    print(f"  {key}: shape={value.shape}, dtype={value.dtype}, NaN={nan_count}, Inf={inf_count}")
                    if key == 'stim':
                        print(f"    stim range: [{value.min().item():.4f}, {value.max().item():.4f}]")
                    elif key == 'robs':
                        print(f"    robs range: [{value.min().item():.4f}, {value.max().item():.4f}]")

            # Check model output
            try:
                with torch.no_grad():
                    stimulus = batch['stim']
                    behavior = batch.get('behavior', None)
                    dataset_idx = 0  # Assume first dataset for debugging

                    rhat = pl_module.model(stimulus, dataset_idx, behavior)

                    nan_count = torch.isnan(rhat).sum().item()
                    inf_count = torch.isinf(rhat).sum().item()
                    neg_count = (rhat < 0).sum().item()

                    print(f"  Model output (rhat): shape={rhat.shape}, dtype={rhat.dtype}")
                    print(f"    NaN={nan_count}, Inf={inf_count}, Negative={neg_count}")
                    print(f"    Range: [{rhat.min().item():.4f}, {rhat.max().item():.4f}]")

                    # Check loss computation
                    batch_with_pred = batch.copy()
                    batch_with_pred['rhat'] = rhat

                    # Test loss computation
                    try:
                        loss = pl_module.loss_fn(batch_with_pred)
                        print(f"    Loss: {loss.item():.6f} (NaN: {torch.isnan(loss).item()})")
                    except Exception as e:
                        print(f"    Loss computation failed: {e}")

            except Exception as e:
                print(f"  Model forward failed: {e}")


class DebugCallback(pl.Callback):
    """
    Callback to help debug hanging issues during training.
    """
    def __init__(self):
        self.step_count = 0

    def on_train_batch_start(self, trainer, pl_module, batch, batch_idx):
        print(f"[Debug] Starting training batch {batch_idx}")

    def on_train_batch_end(self, trainer, pl_module, outputs, batch, batch_idx):
        print(f"[Debug] Completed training batch {batch_idx}")

    def on_validation_batch_start(self, trainer, pl_module, batch, batch_idx, dataloader_idx=0):
        print(f"[Debug] Starting validation batch {batch_idx}")

    def on_validation_batch_end(self, trainer, pl_module, outputs, batch, batch_idx, dataloader_idx=0):
        print(f"[Debug] Completed validation batch {batch_idx}")

    def on_sanity_check_start(self, trainer, pl_module):
        print("[Debug] Starting sanity check...")

    def on_sanity_check_end(self, trainer, pl_module):
        print("[Debug] Sanity check completed!")

    def on_train_start(self, trainer, pl_module):
        print("[Debug] Training started!")

    def on_validation_start(self, trainer, pl_module):
        print("[Debug] Validation started!")

    def on_validation_end(self, trainer, pl_module):
        print("[Debug] Validation ended!")



def train_multidataset_model(train_config_path, model_name=None, device=None, max_epochs=25,
                           compile=False, test_mode=False, early_stopping_patience=5, batch_size=256,
                           use_fp16=False, fp16_opt_level="O1", skip_sanity_check=False, debug_mode=False,
                           debug_dataloader=False, num_workers=None):
    """Train a multidataset model and save checkpoints.

    Args:
        train_config_path: Path to the multidataset training configuration file
        model_name: Model name to use (if None, will be generated from config)
        device: Device specification (string or torch.device)
        max_epochs: Maximum number of epochs to train
        compile: Whether to compile the model with torch.compile
        test_mode: If True, limit batches and enable profiler for testing
        early_stopping_patience: Number of epochs with no improvement after which training will be stopped
        batch_size: Batch size for training and validation
        use_fp16: Whether to use FP16 mixed precision training
        fp16_opt_level: FP16 optimization level ("O1" for conservative, "O2" for aggressive)
        skip_sanity_check: If True, skip PyTorch Lightning sanity validation check
        debug_mode: If True, enable additional debugging features
    """
    print(f"Loading multidataset training config from: {train_config_path}")

    # Load configurations using the new multidataset config loader
    model_config, dataset_configs = load_multidataset_config(train_config_path)

    print(f"Loaded model config (type: {model_config.get('model_type')})")
    print(f"Loaded {len(dataset_configs)} dataset configs")

    # Print dataset info
    for i, dataset_config in enumerate(dataset_configs):
        print(f"Dataset {i}:")
        print(f"  Frontend: {dataset_config.get('frontend', {}).get('type', 'none')}")
        print(f"  Readout: {dataset_config.get('readout', {}).get('type', 'none')}")
        print(f"  CIDs: {len(dataset_config.get('cids', []))} units")
        print(f"  Weight: {dataset_config.get('_weight', 1.0)}")

    # Prepare multidataset data
    train_datasets_dict, val_datasets_dict, updated_dataset_configs = prepare_multidataset_data(
        dataset_configs, use_fp16=use_fp16
    )

    # Create combined dataloaders
    train_loader, val_loader = create_multidataset_loaders(
        train_datasets_dict, val_datasets_dict,
        batch_size=batch_size,
        num_workers=num_workers,
        debug_mode=debug_dataloader or debug_mode
    )

    # Check if DataLoader creation failed
    if train_loader is None or val_loader is None:
        print("❌ DataLoader creation failed! Exiting...")
        return None

    # Generate model name if not provided
    if model_name is None:
        base_name = get_name_from_config(model_config)
        random_suffix = codename.codename()
        model_name = f"{base_name}_multidataset_{random_suffix}"

    print(f"Model name: {model_name}")

    # Handle device specification
    if device is None or device == 'auto':
        device = get_free_device()
        print(f"Auto-selected device: {device}")
    elif isinstance(device, str):
        if device.lower() == 'cpu':
            device = torch.device('cpu')
        elif device.lower().startswith('cuda'):
            device = torch.device(device)
        else:
            raise ValueError(f"Invalid device specification: {device}")
        print(f"Using specified device: {device}")

    # Determine accelerator and devices for PyTorch Lightning
    if device.type == 'cpu':
        accelerator = 'cpu'
        devices = 'auto'
    elif device.type == 'cuda':
        accelerator = 'gpu'
        devices = [device.index] if device.index is not None else 'auto'
    else:
        accelerator = 'auto'
        devices = 'auto'

    # Create checkpoint directory
    checkpoint_dir = Path(f"/mnt/ssd/YatesMarmoV1/conv_model_fits/runs/{model_name}")
    checkpoint_dir.mkdir(parents=True, exist_ok=True)

    # Create callbacks
    checkpoint_callback = pl.pytorch.callbacks.ModelCheckpoint(
        dirpath=str(checkpoint_dir),
        filename=model_name+'-{epoch:02d}-{val_loss_total:.4f}',
        monitor='val_loss_total',
        mode='min',
        save_top_k=2,
        save_last=True
    )

    early_stopping_callback = pl.pytorch.callbacks.EarlyStopping(
        monitor='val_loss_total',
        patience=early_stopping_patience,
        verbose=True,
        mode='min'
    )

    callbacks = [checkpoint_callback, early_stopping_callback, ValidateOnTrainStart()]

    # Add FP16 safety callback if using FP16
    if use_fp16:
        print("Adding FP16 safety callback to trainer...")
        fp16_safety_cb = FP16SafetyCallback()
        callbacks.append(fp16_safety_cb)

        print("Adding gradient monitoring callback to trainer...")
        grad_monitor_cb = GradientMonitorCallback(log_frequency=100)
        callbacks.append(grad_monitor_cb)

        print("Adding NaN debugging callback to trainer...")
        nan_debug_cb = NaNDebugCallback(check_every_n_steps=10)
        callbacks.append(nan_debug_cb)

    # Add debug callback if requested
    if debug_mode:
        print("Adding debug callback to trainer...")
        debug_cb = DebugCallback()
        callbacks.append(debug_cb)

    # Add compile callback if requested
    if (not 'ipykernel' in sys.modules) and compile:
        print("Adding compile callback to trainer...")
        compile_cb = CompileSubmodules(
            convnet=True,
            recurrent=True,
            compile_kwargs=dict(fullgraph=False, dynamic=True, mode="default")
        )
        callbacks.append(compile_cb)

    # Configure profiler
    profiler = None
    if test_mode:
        print("Test mode enabled: Using PyTorch profiler")
        profiler = PyTorchProfiler(
            dirpath=str(checkpoint_dir),
            filename="profiler-traces",
            schedule=torch.profiler.schedule(wait=1, warmup=1, active=3),
            record_shapes=True,
            profile_memory=True,
            with_stack=True
        )
    else:
        profiler = "simple"

    # Calculate baseline firing rates for each dataset
    print("\nCalculating baseline firing rates for each dataset...")
    baseline_rates_list = []

    for dataset_idx, (dataset_name, dataset_config) in enumerate(zip(train_datasets_dict.keys(), updated_dataset_configs)):
        train_dset = train_datasets_dict[dataset_name]

        fr = 0
        n = 0
        for dset in train_dset.dsets:
            fr += dset.covariates['robs'].sum(0)
            n += dset.covariates['robs'].shape[0]
        baseline_rates = fr / n
        baseline_rates_list.append(baseline_rates)

        print(f"Dataset {dataset_idx}: {len(dataset_config['cids'])} units, "
              f"mean rate: {baseline_rates.mean().item():.4f}")

    inv_softplus = lambda x, beta=1: torch.log(torch.exp(beta*x) - 1) / beta
    baseline_rates_list = [inv_softplus(rates) for rates in baseline_rates_list]

    # Adjust learning rate for FP16 training
    base_lr = 5e-4
    if use_fp16:
        # Reduce learning rate for FP16 stability
        if 'args' in locals() and args.fp16_conservative:
            base_lr = 1e-4  # Very conservative
            print(f"Using conservative FP16 learning rate: {base_lr}")
        else:
            base_lr = 2.5e-4  # Standard reduction
            print(f"Reduced learning rate to {base_lr} for FP16 training")

    # Create Lightning module with proper model instantiation for v1multi
    def create_multidataset_model(model_config, dataset_configs):
        """Create a multidataset model with proper configuration."""
        return build_model(model_config, dataset_configs)

    pl_model = MultiDatasetPLCore(
        model_class=create_multidataset_model,
        model_config=model_config,
        dataset_configs=updated_dataset_configs,
        optimizer='AdamW',
        optim_kwargs={'lr': base_lr, 'weight_decay': 1e-5},
        accumulate_grad_batches=1,
        dataset_info=updated_dataset_configs
    )

    # Configure trainer
    trainer_kwargs = {
        "callbacks": callbacks,
        "accelerator": accelerator,
        "devices": devices,
        "logger": WandbLogger(
            project='Digital Twin Multidataset',
            name=model_name,
            save_code=True,
            entity='yateslab',
            save_dir=str(checkpoint_dir),
        ),
        "default_root_dir": str(checkpoint_dir),
        "max_epochs": max_epochs if not test_mode else 1,
        "check_val_every_n_epoch": 1,
        "profiler": profiler,
    }

    # Add FP16 configuration if requested
    if use_fp16:
        trainer_kwargs["precision"] = "16-mixed"

        # Adjust gradient clipping based on conservative mode
        if 'args' in locals() and args.fp16_conservative:
            trainer_kwargs["gradient_clip_val"] = 0.5  # More aggressive clipping
            print(f"FP16 training enabled with conservative settings: precision='16-mixed', gradient clipping=0.5")
        else:
            trainer_kwargs["gradient_clip_val"] = 1.0  # Standard clipping
            print(f"FP16 training enabled with precision='16-mixed' and gradient clipping=1.0")

    # Skip sanity check if requested (useful for debugging hanging issues)
    if skip_sanity_check:
        trainer_kwargs["num_sanity_val_steps"] = 0
        print("Skipping PyTorch Lightning sanity validation check")

    # Add debug options if requested
    if debug_mode:
        trainer_kwargs["detect_anomaly"] = True
        trainer_kwargs["track_grad_norm"] = 2
        print("Debug mode enabled: anomaly detection and gradient norm tracking")

    # Add batch limits if in test mode
    if test_mode:
        trainer_kwargs.update({
            "limit_train_batches": 5,
            "limit_val_batches": 3,
        })

    # Initialize model components
    initialize_model_components(pl_model.model, init_bias=baseline_rates_list)

    # Create trainer and train
    trainer = pl.Trainer(**trainer_kwargs)

    # Start timing
    start_time = time.time()

    # Train the model
    trainer.fit(pl_model, train_loader, val_loader)

    # End timing
    end_time = time.time()
    training_time = end_time - start_time

    # Get the best model path
    best_model_path = checkpoint_callback.best_model_path

    # Print training summary
    print("\n===== MULTIDATASET TRAINING SUMMARY =====")
    print(f"Total training time: {training_time:.2f} seconds")
    print(f"Number of datasets: {len(dataset_configs)}")

    for i, dataset_config in enumerate(updated_dataset_configs):
        print(f"Dataset {i}: {len(dataset_config['cids'])} units")

    # Print metrics
    print("\nTraining metrics:")
    for metric_name, metric_value in trainer.callback_metrics.items():
        if isinstance(metric_value, torch.Tensor):
            print(f"- {metric_name}: {metric_value.item():.6f}")
        else:
            print(f"- {metric_name}: {metric_value}")

    print("==========================================")

    # Register the model
    model_id = f"{model_name}_epoch_{trainer.current_epoch}"

    # Create metadata for multidataset model
    metadata = {
        'epochs': trainer.current_epoch,
        'training_time': training_time,
        'batch_size': batch_size,
        'compiled': compile,
        'num_datasets': len(dataset_configs),
        'total_units': sum(len(config['cids']) for config in updated_dataset_configs)
    }

    registry.register_model(
        model_id=model_id,
        checkpoint_path=best_model_path,
        config_path=str(train_config_path),  # Store the training config path
        metrics={'val_loss_total': trainer.callback_metrics.get('val_loss_total', 0).item()},
        metadata=metadata,
        dataset_config_path=[config.get('_config_path', '') for config in updated_dataset_configs]
    )

    return best_model_path

#%% Main execution
def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='Train multidataset neural network models with FP16 support')

    # Required training configuration
    parser.add_argument('--train-config', type=str, required=True,
                        help='Path to multidataset training configuration file')

    # Training parameters
    parser.add_argument('--compile', action='store_true',
                        help='Enable model compilation with torch.compile')

    parser.add_argument('--test', action='store_true',
                        help='Run in test mode with limited batches and profiling')

    parser.add_argument('--max-epochs', type=int, default=25,
                        help='Maximum number of training epochs')

    parser.add_argument('--batch-size', type=int, default=256,
                        help='Batch size for training and validation')

    parser.add_argument('--early-stopping-patience', type=int, default=5,
                        help='Number of epochs with no improvement after which training will be stopped')

    parser.add_argument('--device', type=str, default='auto',
                        help='Device to use for training. Options: "auto", "cpu", "cuda", "cuda:0", etc. Default: "auto" (automatically select best available device)')

    parser.add_argument('--no-codename', action='store_true',
                        help='Disable appending a random codename to the model name (codename is appended by default for uniqueness)')

    # FP16 mixed precision training
    parser.add_argument('--fp16', action='store_true',
                        help='Enable FP16 mixed precision training for memory efficiency and speed')

    parser.add_argument('--fp16-opt-level', type=str, default='O1', choices=['O1', 'O2'],
                        help='FP16 optimization level: O1 (conservative) or O2 (aggressive). Default: O1')

    # Debugging options
    parser.add_argument('--skip-sanity-check', action='store_true',
                        help='Skip PyTorch Lightning sanity validation check (useful if hanging)')

    parser.add_argument('--debug', action='store_true',
                        help='Enable debug mode with anomaly detection and gradient tracking')

    parser.add_argument('--debug-dataloader', action='store_true',
                        help='Enable DataLoader debugging (test data loading before training)')

    parser.add_argument('--num-workers', type=int, default=None,
                        help='Override number of DataLoader workers (default: auto-detect)')

    # Advanced FP16 options
    parser.add_argument('--fp16-conservative', action='store_true',
                        help='Use more conservative FP16 settings (lower LR, more clipping)')

    parser.add_argument('--fp16-init-scale', type=float, default=2**16,
                        help='Initial gradient scaling for FP16 (default: 65536)')

    return parser.parse_args()

if __name__ == "__main__":
    # Parse command-line arguments
    args = parse_arguments()

    print("=== MULTIDATASET TRAINING WITH FP16 SUPPORT ===")
    print(f"Training config: {args.train_config}")
    print(f"Device: {args.device}")
    print(f"Compilation enabled: {args.compile}")
    print(f"Test mode: {args.test}")
    print(f"Early stopping patience: {args.early_stopping_patience}")
    print(f"Append codename: {not args.no_codename}")
    print(f"FP16 training: {args.fp16}")
    if args.fp16:
        print(f"FP16 optimization level: {args.fp16_opt_level}")

    # Generate model name with codename if requested
    model_name = None
    if not args.no_codename:
        # We'll generate the name in the training function
        pass

    # Train multidataset model
    best_model_path = train_multidataset_model(
        train_config_path=args.train_config,
        model_name=model_name,
        device=args.device,
        max_epochs=args.max_epochs,
        compile=args.compile,
        test_mode=args.test,
        early_stopping_patience=args.early_stopping_patience,
        batch_size=args.batch_size,
        use_fp16=args.fp16,
        fp16_opt_level=args.fp16_opt_level,
        skip_sanity_check=args.skip_sanity_check,
        debug_mode=args.debug,
        debug_dataloader=args.debug_dataloader,
        num_workers=args.num_workers
    )

    print(f"\n🎉 Multidataset training complete!")
    print(f"Best model saved to: {best_model_path}")
    print("\nDone!")