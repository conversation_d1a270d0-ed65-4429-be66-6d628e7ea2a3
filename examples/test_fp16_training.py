#!/usr/bin/env python
"""
Test script for FP16 training implementation.

This script validates that FP16 training works correctly by:
1. Running a short training session with FP16
2. Checking for gradient issues
3. Comparing memory usage
4. Validating model outputs

Usage:
    python examples/test_fp16_training.py --config path/to/multidataset_config.yaml
"""

import torch
import argparse
import sys
import time
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent.parent))

from examples.model_train_multi import train_multidataset_model
import psutil

def get_memory_usage():
    """Get current memory usage statistics."""
    # CPU memory
    cpu_memory = psutil.virtual_memory()
    cpu_used_gb = cpu_memory.used / (1024**3)
    
    # GPU memory
    gpu_memory = 0
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.memory_allocated() / (1024**3)
    
    return cpu_used_gb, gpu_memory

def test_fp16_training(config_path, test_duration_epochs=2):
    """
    Test FP16 training implementation.
    
    Args:
        config_path: Path to multidataset training configuration
        test_duration_epochs: Number of epochs to run for testing
    """
    print("=" * 60)
    print("FP16 Training Implementation Test")
    print("=" * 60)
    
    # Test configurations
    test_configs = [
        {"name": "FP32 Baseline", "use_fp16": False},
        {"name": "FP16 Mixed Precision", "use_fp16": True},
    ]
    
    results = {}
    
    for config in test_configs:
        print(f"\n--- Testing {config['name']} ---")
        
        # Clear GPU cache
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # Record initial memory
        cpu_before, gpu_before = get_memory_usage()
        
        # Record start time
        start_time = time.time()
        
        try:
            # Run training
            best_model_path = train_multidataset_model(
                train_config_path=config_path,
                model_name=f"test_{config['name'].lower().replace(' ', '_')}",
                device='auto',
                max_epochs=test_duration_epochs,
                compile=False,
                test_mode=True,  # Enable test mode for faster execution
                early_stopping_patience=10,
                batch_size=64,  # Smaller batch size for testing
                use_fp16=config['use_fp16'],
                fp16_opt_level="O1"
            )
            
            # Record end time
            end_time = time.time()
            training_time = end_time - start_time
            
            # Record final memory
            cpu_after, gpu_after = get_memory_usage()
            
            # Store results
            results[config['name']] = {
                'success': True,
                'training_time': training_time,
                'cpu_memory_delta': cpu_after - cpu_before,
                'gpu_memory_delta': gpu_after - gpu_before,
                'model_path': best_model_path
            }
            
            print(f"✅ {config['name']} completed successfully")
            print(f"   Training time: {training_time:.2f}s")
            print(f"   CPU memory delta: {cpu_after - cpu_before:.2f} GB")
            print(f"   GPU memory delta: {gpu_after - gpu_before:.2f} GB")
            
        except Exception as e:
            print(f"❌ {config['name']} failed: {str(e)}")
            results[config['name']] = {
                'success': False,
                'error': str(e)
            }
    
    # Print comparison
    print("\n" + "=" * 60)
    print("COMPARISON RESULTS")
    print("=" * 60)
    
    if all(result['success'] for result in results.values()):
        fp32_result = results["FP32 Baseline"]
        fp16_result = results["FP16 Mixed Precision"]
        
        # Calculate improvements
        time_ratio = fp32_result['training_time'] / fp16_result['training_time']
        memory_savings = fp32_result['gpu_memory_delta'] - fp16_result['gpu_memory_delta']
        memory_savings_pct = (memory_savings / fp32_result['gpu_memory_delta']) * 100 if fp32_result['gpu_memory_delta'] > 0 else 0
        
        print(f"Speed improvement: {time_ratio:.2f}x")
        print(f"GPU memory savings: {memory_savings:.2f} GB ({memory_savings_pct:.1f}%)")
        
        # Validation checks
        print("\n--- Validation Checks ---")
        
        if time_ratio > 0.8:  # Should be at least as fast
            print("✅ Speed: FP16 training is appropriately fast")
        else:
            print("⚠️  Speed: FP16 training is slower than expected")
        
        if memory_savings > 0:
            print("✅ Memory: FP16 training uses less GPU memory")
        else:
            print("⚠️  Memory: FP16 training did not reduce memory usage")
        
        if memory_savings_pct > 20:
            print("✅ Memory efficiency: Significant memory savings achieved")
        elif memory_savings_pct > 10:
            print("✅ Memory efficiency: Moderate memory savings achieved")
        else:
            print("⚠️  Memory efficiency: Limited memory savings")
    
    else:
        print("❌ Cannot compare results due to training failures")
        for name, result in results.items():
            if not result['success']:
                print(f"   {name}: {result['error']}")
    
    print("\n--- Test Summary ---")
    success_count = sum(1 for result in results.values() if result['success'])
    total_count = len(results)
    print(f"Successful tests: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 All tests passed! FP16 implementation is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

def main():
    parser = argparse.ArgumentParser(description='Test FP16 training implementation')
    parser.add_argument('--train-config', type=str, required=True,
                        help='Path to multidataset training configuration file')
    parser.add_argument('--epochs', type=int, default=2,
                        help='Number of epochs to run for testing (default: 2)')

    args = parser.parse_args()

    # Validate config file exists
    config_path = Path(args.train_config)
    if not config_path.exists():
        print(f"Error: Configuration file not found: {config_path}")
        sys.exit(1)

    # Check GPU availability
    if not torch.cuda.is_available():
        print("Warning: CUDA not available. FP16 benefits will be limited.")

    # Run tests
    success = test_fp16_training(str(config_path), args.epochs)

    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
